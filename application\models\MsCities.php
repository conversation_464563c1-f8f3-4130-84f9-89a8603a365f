<?php
defined('BASEPATH') or die('No direct script access allowed!');

class MsCities extends MY_Model
{
    protected $table = 'mscities';

    public function insert($data = [])
    {
        // Auto-generate city code based on province code
        $provinceCode = $data['province_code'];

        $lastCode = $this->db->select_max('code')
            ->from($this->table)
            ->where('province_code', $provinceCode)
            ->get()
            ->row();

        if ($lastCode && $lastCode->code) {
            // Extract the last 2 digits and increment
            $lastNumber = (int)substr($lastCode->code, -2);
            $newNumber = str_pad($lastNumber + 1, 2, '0', STR_PAD_LEFT);
            $newCode = $provinceCode . $newNumber;
        } else {
            $newCode = $provinceCode . '01';
        }

        $data['code'] = $newCode;
        $data['createddate'] = getCurrentDate();
        $data['createdby'] = getCurrentIdUser();

        return parent::insert($data);
    }
}

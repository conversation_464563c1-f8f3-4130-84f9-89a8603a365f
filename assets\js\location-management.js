/**
 * Location Management JavaScript
 * Handles cascading dropdowns for Province -> City -> District -> Village
 */

function loadCities(provinceCode) {
    const citySelect = document.querySelector('select[name="city_code"], select[name="required_city_code"]');
    const districtSelect = document.querySelector('select[name="district_code"], select[name="required_district_code"]');
    const villageSelect = document.querySelector('select[name="village_code"], select[name="required_village_code"]');

    if (!citySelect) return;

    // Clear city, district and village dropdowns
    citySelect.innerHTML = '<option value="">Pilih <PERSON>/<PERSON></option>';
    if (districtSelect) {
        districtSelect.innerHTML = '<option value="">Pilih <PERSON>cam<PERSON>n</option>';
    }
    if (villageSelect) {
        villageSelect.innerHTML = '<option value="">Pilih Desa</option>';
    }

    if (!provinceCode) return;

    // Load cities based on province
    fetch(base_url + 'api/get_cities_by_province', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'province_code=' + encodeURIComponent(provinceCode)
    })
        .then(response => response.json())
        .then(data => {
            if (data.RESULT === 'OK') {
                data.DATA.forEach(city => {
                    const option = document.createElement('option');
                    option.value = city.code;
                    option.textContent = city.name;
                    citySelect.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error loading cities:', error);
        });
}

function loadDistricts(cityCode) {
    const districtSelect = document.querySelector('select[name="district_code"], select[name="required_district_code"]');
    const villageSelect = document.querySelector('select[name="village_code"], select[name="required_village_code"]');

    if (!districtSelect) return;

    // Clear district and village dropdowns
    districtSelect.innerHTML = '<option value="">Pilih Kecamatan</option>';
    if (villageSelect) {
        villageSelect.innerHTML = '<option value="">Pilih Desa</option>';
    }

    if (!cityCode) return;

    // Load districts based on city
    fetch(base_url + 'api/get_districts_by_city', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'city_code=' + encodeURIComponent(cityCode)
    })
        .then(response => response.json())
        .then(data => {
            if (data.RESULT === 'OK') {
                data.DATA.forEach(district => {
                    const option = document.createElement('option');
                    option.value = district.code;
                    option.textContent = district.name;
                    districtSelect.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error loading districts:', error);
        });
}

function loadVillages(districtCode) {
    const villageSelect = document.querySelector('select[name="village_code"], select[name="required_village_code"]');

    if (!villageSelect) return;

    // Clear village dropdown
    villageSelect.innerHTML = '<option value="">Pilih Desa</option>';

    if (!districtCode) return;

    // Load villages based on district
    fetch(base_url + 'api/get_villages_by_district', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'district_code=' + encodeURIComponent(districtCode)
    })
        .then(response => response.json())
        .then(data => {
            if (data.RESULT === 'OK') {
                data.DATA.forEach(village => {
                    const option = document.createElement('option');
                    option.value = village.code;
                    option.textContent = village.name;
                    villageSelect.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error loading villages:', error);
        });
}

// Initialize cascading dropdowns on page load
window.onload = function () {
    // Set up event listeners for cascading dropdowns
    const provinceSelect = document.querySelector('select[name="province_code"]');
    const citySelect = document.querySelector('select[name="city_code"], select[name="required_city_code"]');
    const districtSelect = document.querySelector('select[name="district_code"], select[name="required_district_code"]');

    if (provinceSelect) {
        provinceSelect.addEventListener('change', function () {
            loadCities(this.value);
        });
    }

    if (citySelect) {
        citySelect.addEventListener('change', function () {
            loadDistricts(this.value);
        });
    }

    if (districtSelect) {
        districtSelect.addEventListener('change', function () {
            loadVillages(this.value);
        });
    }
};

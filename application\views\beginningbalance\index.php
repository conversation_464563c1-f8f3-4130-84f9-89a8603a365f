<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
    <h6 class="fw-semibold mb-0">Daftar Saldo Awal</h6>

    <ul class="d-flex align-items-center gap-2">
        <li class="fw-medium">
            <a href="<?= base_url() ?>" class="d-flex align-items-center gap-1 hover-text-primary">
                <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                Dashboard
            </a>
        </li>
        <li>-</li>
        <li class="fw-medium">Daftar Saldo Awal</li>
    </ul>
</div>

<div class="card basic-data-table">
    <div class="card-header d-flex justify-content-between align-items-center">
        <div>
            <h5 class="card-title mb-0">Daftar Saldo Awal</h5>
        </div>

        <div>
            <a href="<?= base_url('master/beginningbalance/add') ?>" class="btn btn-primary btn-sm">
                <i class="ri-add-line"></i>
                <span>Tambah</span>
            </a>
        </div>
    </div>

    <div class="card-body">
        <!-- Filter Section -->
        <div class="row mb-3">
            <div class="col-md-4">
                <label for="filter_workunit" class="form-label">Filter Unit Usaha</label>
                <select id="filter_workunit" class="form-select">
                    <option value="">Semua Unit Usaha</option>
                    <?php
                    // Get unique work units from the results
                    $unique_workunits = array();
                    foreach ($element['result'] as $item) {
                        if (!isset($unique_workunits[$item->workunitdata])) {
                            $unique_workunits[$item->workunitdata] = $item->workunitdata;
                        }
                    }
                    foreach ($unique_workunits as $workunit): ?>
                        <option value="<?= $workunit ?>"><?= $workunit ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table bordered-table datatables">
                <thead>
                    <tr>
                        <th>Unit Usaha</th>
                        <th>Periode</th>
                        <th>Saldo Awal</th>
                        <th>Aksi</th>
                    </tr>
                </thead>

                <tbody>
                    <?php foreach ($element['result'] as $key => $value): ?>
                        <tr>
                            <td><?= $value->workunitdata ?></td>
                            <td><?= date('Y', strtotime($value->period)) ?></td>
                            <td>Rp <?= number_format($value->beginning_balances, 0, ',', '.') ?></td>
                            <td>
                                <button type="button" class="btn btn-success btn-sm me-1" onclick="showAddBalanceModal(<?= $value->id ?>)">
                                    <i class="ri-add-line"></i>
                                    <span>Tambah</span>
                                </button>
                                <button type="button" class="btn btn-warning btn-sm me-1" onclick="showReduceBalanceModal(<?= $value->id ?>)">
                                    <i class="ri-subtract-line"></i>
                                    <span>Kurang</span>
                                </button>
                                <button type="button" class="btn btn-info btn-sm" onclick="showBalanceHistory(<?= $value->id ?>)">
                                    <i class="ri-history-line"></i>
                                    <span>Riwayat</span>
                                </button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal Tambah Saldo -->
<div class="modal fade" id="addBalanceModal" tabindex="-1" aria-labelledby="addBalanceModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content radius-16 bg-base">
            <div class="modal-header py-16 px-24 border border-top-0 border-start-0 border-end-0">
                <h1 class="modal-title fs-5" id="addBalanceModalLabel">
                    Tambah Saldo Awal
                </h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addBalanceForm">
                <div class="modal-body p-24">
                    <input type="hidden" id="add_beginningbalance_id" name="beginningbalance_id">
                    <div class="row">
                        <div class="col-12 mb-20">
                            <label for="add_amount" class="form-label fw-semibold text-primary-light text-sm mb-8">
                                Nominal <span class="text-danger">*</span>
                            </label>
                            <div class="position-relative">
                                <input type="number" class="form-control radius-8 ps-40" id="add_amount" name="amount" placeholder="Masukkan nominal" required min="1">
                                <span class="position-absolute start-0 top-50 translate-middle-y ms-12 line-height-1 text-secondary-light fw-medium">Rp</span>
                            </div>
                        </div>
                        <div class="col-12 mb-20">
                            <label for="add_description" class="form-label fw-semibold text-primary-light text-sm mb-8">Keterangan</label>
                            <textarea class="form-control radius-8" id="add_description" name="description" rows="4" placeholder="Masukkan keterangan (opsional)"></textarea>
                        </div>
                        <div class="d-flex align-items-center justify-content-end gap-3 mt-24">
                            <button type="button" class="border border-danger-600 bg-hover-danger-200 text-danger-600 text-md px-40 py-11 radius-8" data-bs-dismiss="modal">
                                Batal
                            </button>
                            <button type="submit" class="btn btn-success border border-success-600 text-md px-24 py-12 radius-8">
                                Tambah Saldo
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Kurang Saldo -->
<div class="modal fade" id="reduceBalanceModal" tabindex="-1" aria-labelledby="reduceBalanceModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content radius-16 bg-base">
            <div class="modal-header py-16 px-24 border border-top-0 border-start-0 border-end-0">
                <h1 class="modal-title fs-5" id="reduceBalanceModalLabel">
                    Kurang Saldo Awal
                </h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="reduceBalanceForm">
                <div class="modal-body p-24">
                    <input type="hidden" id="reduce_beginningbalance_id" name="beginningbalance_id">
                    <div class="row">
                        <div class="col-12 mb-20">
                            <label for="reduce_amount" class="form-label fw-semibold text-primary-light text-sm mb-8">
                                Nominal <span class="text-danger">*</span>
                            </label>
                            <div class="position-relative">
                                <input type="number" class="form-control radius-8 ps-40" id="reduce_amount" name="amount" placeholder="Masukkan nominal" required min="1">
                                <span class="position-absolute start-0 top-50 translate-middle-y ms-12 line-height-1 text-secondary-light fw-medium">Rp</span>
                            </div>
                        </div>
                        <div class="col-12 mb-20">
                            <label for="reduce_description" class="form-label fw-semibold text-primary-light text-sm mb-8">Keterangan</label>
                            <textarea class="form-control radius-8" id="reduce_description" name="description" rows="4" placeholder="Masukkan keterangan (opsional)"></textarea>
                        </div>
                        <div class="d-flex align-items-center justify-content-end gap-3 mt-24">
                            <button type="button" class="border border-danger-600 bg-hover-danger-200 text-danger-600 text-md px-40 py-11 radius-8" data-bs-dismiss="modal">
                                Batal
                            </button>
                            <button type="submit" class="btn btn-warning border border-warning-600 text-md px-24 py-12 radius-8">
                                Kurang Saldo
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Riwayat Saldo -->
<div class="modal fade" id="historyModal" tabindex="-1" aria-labelledby="historyModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content radius-16 bg-base">
            <div class="modal-header py-16 px-24 border border-top-0 border-start-0 border-end-0">
                <h1 class="modal-title fs-5" id="historyModalLabel">
                    Riwayat Saldo Awal
                </h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-24">
                <div id="historyContent">
                    <div class="text-center py-40">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="text-secondary-light mt-12">Memuat riwayat saldo...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer py-16 px-24 border border-bottom-0 border-start-0 border-end-0">
                <button type="button" class="btn btn-secondary border border-secondary-600 text-md px-24 py-12 radius-8" data-bs-dismiss="modal">
                    Tutup
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    window.onload = function() {

        // Global functions for modal handling
        window.showAddBalanceModal = function(id) {
            $('#add_beginningbalance_id').val(id);
            $('#addBalanceModal').modal('show');
        };

        window.showReduceBalanceModal = function(id) {
            $('#reduce_beginningbalance_id').val(id);
            $('#reduceBalanceModal').modal('show');
        };

        window.showBalanceHistory = function(id) {
            $('#historyContent').html('<div class="text-center"><div class="spinner-border" role="status"></div></div>');
            $('#historyModal').modal('show');

            $.ajax({
                url: '<?= base_url('balance/history/') ?>' + id,
                method: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.RESULT == 'OK') {
                        let content = '';

                        // History Table
                        content += '<div class="table-responsive">';
                        content += '<table class="table bordered-table mb-0">';
                        content += '<thead>';
                        content += '<tr>';
                        content += '<th scope="col" class="text-sm fw-semibold text-primary-light">Tanggal</th>';
                        content += '<th scope="col" class="text-sm fw-semibold text-primary-light">Tipe</th>';
                        content += '<th scope="col" class="text-sm fw-semibold text-primary-light">Nominal</th>';
                        content += '<th scope="col" class="text-sm fw-semibold text-primary-light">Keterangan</th>';
                        content += '</tr>';
                        content += '</thead>';
                        content += '<tbody>';

                        if (response.DATA.history.length > 0) {
                            response.DATA.history.forEach(function(item) {
                                let badgeClass = item.type == 'Tambah' ? 'bg-success-600' : 'bg-warning-600';
                                let iconClass = item.type == 'Tambah' ? 'fa6-regular:square-plus' : 'ri-subtract-line';

                                content += '<tr>';
                                content += '<td class="text-sm text-secondary-light">' + new Date(item.createddate).toLocaleDateString('id-ID', {
                                    year: 'numeric',
                                    month: 'short',
                                    day: 'numeric',
                                    hour: '2-digit',
                                    minute: '2-digit'
                                }) + '</td>';
                                content += '<td>';
                                content += '<span class="badge text-sm fw-semibold px-12 py-4 radius-4 text-white ' + badgeClass + '">';
                                content += item.type;
                                content += '</span>';
                                content += '</td>';
                                content += '<td class="text-sm fw-semibold text-primary-light">Rp ' + new Intl.NumberFormat('id-ID').format(item.amount) + '</td>';
                                content += '<td class="text-sm text-secondary-light">' + (item.description || '<em class="text-neutral-400">Tidak ada keterangan</em>') + '</td>';
                                content += '</tr>';
                            });
                        } else {
                            content += '<tr>';
                            content += '<td colspan="4" class="text-center py-40">';
                            content += '<iconify-icon icon="solar:history-3-outline" class="icon text-4xl text-neutral-400 mb-12"></iconify-icon>';
                            content += '<p class="text-neutral-400 mb-0">Belum ada riwayat perubahan saldo</p>';
                            content += '</td>';
                            content += '</tr>';
                        }

                        content += '</tbody></table></div>';
                        $('#historyContent').html(content);
                    } else {
                        $('#historyContent').html('<div class="alert alert-danger radius-8">' + response.MESSAGE + '</div>');
                    }
                },
                error: function() {
                    $('#historyContent').html(
                        '<div class="text-center py-40">' +
                        '<iconify-icon icon="solar:danger-circle-outline" class="icon text-4xl text-danger-600 mb-12"></iconify-icon>' +
                        '<p class="text-danger-600 mb-0">Gagal memuat riwayat saldo</p>' +
                        '<p class="text-secondary-light text-sm mt-8">Silakan coba lagi beberapa saat</p>' +
                        '</div>'
                    );
                }
            });
        };

        // Handle form submissions
        $('#addBalanceForm').on('submit', function(e) {
            e.preventDefault();

            $.ajax({
                url: '<?= base_url('balance/add') ?>',
                method: 'POST',
                data: $(this).serialize(),
                dataType: 'json',
                success: function(response) {
                    if (response.RESULT == 'OK') {
                        $('#addBalanceModal').modal('hide');
                        swal({
                            title: 'Berhasil!',
                            text: response.MESSAGE,
                            icon: 'success',
                            button: 'OK'
                        }).then(() => {
                            location.reload();
                        });
                    } else {
                        swal({
                            title: 'Gagal!',
                            text: response.MESSAGE,
                            icon: 'error',
                            button: 'OK'
                        });
                    }
                },
                error: function() {
                    swal({
                        title: 'Error!',
                        text: 'Terjadi kesalahan',
                        icon: 'error',
                        button: 'OK'
                    });
                }
            });
        });

        $('#reduceBalanceForm').on('submit', function(e) {
            e.preventDefault();

            $.ajax({
                url: '<?= base_url('balance/reduce') ?>',
                method: 'POST',
                data: $(this).serialize(),
                dataType: 'json',
                success: function(response) {
                    if (response.RESULT == 'OK') {
                        $('#reduceBalanceModal').modal('hide');
                        swal({
                            title: 'Berhasil!',
                            text: response.MESSAGE,
                            icon: 'success',
                            button: 'OK'
                        }).then(() => {
                            location.reload();
                        });
                    } else {
                        swal({
                            title: 'Gagal!',
                            text: response.MESSAGE,
                            icon: 'error',
                            button: 'OK'
                        });
                    }
                },
                error: function() {
                    swal({
                        title: 'Error!',
                        text: 'Terjadi kesalahan',
                        icon: 'error',
                        button: 'OK'
                    });
                }
            });
        });

        // Reset forms when modals are hidden
        $('#addBalanceModal').on('hidden.bs.modal', function() {
            $('#addBalanceForm')[0].reset();
        });

        $('#reduceBalanceModal').on('hidden.bs.modal', function() {
            $('#reduceBalanceForm')[0].reset();
        });

        // Filter functionality
        var table = $('.datatables').DataTable();

        // Work unit filter
        $('#filter_workunit').on('change', function() {
            var workunitText = this.value;
            if (workunitText) {
                // Filter by work unit name (column index 0 - Unit Usaha)
                table.column(0).search(workunitText, false, false).draw();
            } else {
                table.column(0).search('').draw();
            }
        });

    };
</script>
<?php
defined('BASEPATH') or die('No direct script access allowed!');

class MsDistricts extends MY_Model
{
    protected $table = 'msdistricts';

    public function insert($data = [])
    {
        // Auto-generate district code based on city code
        $cityCode = $data['city_code'];

        $lastCode = $this->db->select_max('code')
            ->from($this->table)
            ->where('city_code', $cityCode)
            ->get()
            ->row();

        if ($lastCode && $lastCode->code) {
            // Extract the last 2 digits and increment
            $lastNumber = (int)substr($lastCode->code, -2);
            $newNumber = str_pad($lastNumber + 1, 2, '0', STR_PAD_LEFT);
            $newCode = $cityCode . $newNumber;
        } else {
            $newCode = $cityCode . '01';
        }

        $data['code'] = $newCode;
        $data['createddate'] = getCurrentDate();
        $data['createdby'] = getCurrentIdUser();

        return parent::insert($data);
    }
}

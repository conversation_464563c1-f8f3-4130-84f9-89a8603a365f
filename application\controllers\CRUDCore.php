<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property CI_DB_query_builder $db
 * @property CI_Upload $upload
 * @property CRUD $crud
 */
class CRUDCore extends CI_Controller
{
    public function index($feature)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        // Load model with proper naming
        if ($feature == 'beginningbalance') {
            $this->load->model('Beginningbalances', 'beginningbalances');
        } else {
            $this->load->model(ucfirst($feature) . 's', strtolower($feature) . 's');
        }

        $data = array();
        $data['title'] = ucwords($feature);

        // Set feature in crud library first
        $this->crud->setFeature($feature);

        // Use custom view for beginning balance
        if ($feature == 'beginningbalance') {
            $data['content'] = 'beginningbalance/index';
        } else {
            $data['content'] = $this->crud->showContents($feature, 'index');
        }

        $data['element'] = $this->crud->getElements(getContents($feature, 'index'));

        return $this->load->view('master', $data);
    }

    public function add($feature)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        // Load model with proper naming
        if ($feature == 'beginningbalance') {
            $this->load->model('Beginningbalances', 'beginningbalances');
        } else {
            $this->load->model(ucfirst($feature) . 's', strtolower($feature) . 's');
        }

        $data = array();
        $data['title'] = 'Tambah ' . ucwords($feature);
        $data['feature'] = $feature;
        $data['content'] = $this->crud->showContents($feature, 'add');
        $data['element'] = $this->crud->getElements(getContents($feature, 'add'));

        return $this->load->view('master', $data);
    }

    public function process_add($feature)
    {
        try {
            if (!isLogin()) {
                return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses.');
            }

            $this->db->trans_begin();

            // Load model with proper naming
            if ($feature == 'beginningbalance') {
                $this->load->model('Beginningbalances', 'beginningbalances');
                $models = 'beginningbalances';
            } else {
                $this->load->model(ucfirst($feature) . 's', strtolower($feature) . 's');
                $models = $feature . 's';
            }

            $config = array();
            $config['upload_path'] = './uploads';
            $config['encrypt_name'] = true;
            $config['allowed_types'] = '*';

            $this->load->library('upload', $config);

            $fields = getFieldParameter($feature, 'add');
            $client_fields = array();

            $insert = array();
            foreach ($_FILES as $key => $value) {
                if ($value['name'] == null) continue;

                if ($this->upload->do_upload($key)) {
                    $insert[$key] = $this->upload->data('file_name');
                } else {
                    return JSONResponseDefault('FAILED', $this->upload->display_errors('', ''));
                }
            }

            foreach ($_POST as $key => $value) {
                if (str_contains($key, 'validated')) {
                    $col = explode('_', $key);

                    if (count($col) == 2) {
                        $validate = $this->$models->get(array($col[1] => getPost($key)));

                        if ($validate->num_rows() > 0) {
                            return JSONResponseDefault('FAILED', ucwords($col[1]) . ' yang anda masukkan telah terdata');
                        }

                        $insert[$col[1]] = getPost($key);
                    } elseif (count($col) > 2) {
                        if ($col[0] == 'validated' && $col[1] == 'with') {
                            $columns = isset($col[2]) ? $col[2] : null;
                            $table = isset($col[4]) ? $col[4] : null;
                            $parameter = isset($col[5]) ? $col[5] : null;
                            $statement = isset($col[6]) ? $col[6] : null;
                            $options = isset($col[8]) ? $col[8] : null;

                            $get = $this->db->get_where($table, array('id' => getPost($parameter)))->row();

                            switch ($statement) {
                                case 'mustlessthanorsame':
                                    if ($get != null && $get->{$columns} < $value) {
                                        $message = ucwords($col[7]) . " harus kurang dari atau sama dengan jumlah $columns yang tersedia";

                                        return JSONResponseDefault('FAILED', $message);
                                    } elseif ($get != null && $options == 'andthen' && $col[9] == 'update') {
                                        $upd = array();
                                        $upd[$columns] = $get->{$columns} - getPost($key);

                                        $this->db->set($upd)->where('id', $get->id)->update($table);
                                    }

                                    break;
                            }

                            $insert[$col[7]] = getPost($key);
                        } else if ($col[0] == 'required' && $col[1] == 'validated') {
                            $columns = $col[2];

                            $validate = $this->$models->get(array($columns => getPost($key)));

                            if ($validate->num_rows() > 0) {
                                return JSONResponseDefault('FAILED', ucwords($columns) . ' yang anda masukkan telah terdata');
                            }

                            $insert[$columns] = getPost($key);
                        }
                    }
                } else if (str_contains($key, 'required')) {
                    $col = explode('_', $key);

                    array_shift($col);
                    $columns = implode('_', $col);

                    if (getPost($key) == null) {
                        return JSONResponseDefault('FAILED', ucwords($columns) . ' harus diisi');
                    } else if (is_numeric(getPost($key))) {
                        if (getPost($key) < 0) {
                            return JSONResponseDefault('FAILED', ucwords($columns) . ' tidak boleh bernilai negatif');
                        }
                    } else if (is_array(getPost($key))) {
                        if (count(getPost($key)) == 0) {
                            return JSONResponseDefault('FAILED', ucwords($columns) . ' harus dipilih');
                        }
                    }

                    if (str_contains($columns, 'validated')) {
                        $col = explode('_', $columns);

                        $insert[$col[1]] = getPost($key);
                    } else {
                        // Special handling for beginning balance period field
                        if ($feature == 'beginningbalance' && $columns == 'period') {
                            // Convert YYYY to YYYY-01-01 for database storage
                            $periodValue = getPost($key);
                            if (preg_match('/^\d{4}$/', $periodValue)) {
                                $insert[$columns] = $periodValue . '-01-01';
                            } else {
                                $insert[$columns] = $periodValue;
                            }
                        } else {
                            $insert[$columns] = getPost($key);
                        }
                    }
                } else {
                    if (getPost($key) != null) {
                        if (str_contains($key, 'password')) {
                            $insert[$key] = password_hash(getPost($key), PASSWORD_DEFAULT);
                        } else {
                            if (str_contains($key, 'validated')) {
                                $col = explode('_', $key);

                                $insert[$col[1]] = getPost($key);
                            } else {
                                if (is_array(getPost($key))) {
                                    $insert[$key] = implode(',', getPost($key));
                                } else {
                                    // Special handling for beginning balance period field
                                    if ($feature == 'beginningbalance' && $key == 'period') {
                                        // Convert YYYY to YYYY-01-01 for database storage
                                        $periodValue = getPost($key);
                                        if (preg_match('/^\d{4}$/', $periodValue)) {
                                            $insert[$key] = $periodValue . '-01-01';
                                        } else {
                                            $insert[$key] = $periodValue;
                                        }
                                    } else {
                                        $insert[$key] = getPost($key);
                                    }
                                }
                            }
                        }
                    }
                }

                $client_fields[] = $key;
            }

            // Special validation for beginning balance to prevent duplicates
            if ($feature == 'beginningbalance') {
                // Check for duplicate period and work unit combination
                $period = isset($insert['period']) ? $insert['period'] : null;
                $workunitid = isset($insert['workunitid']) ? $insert['workunitid'] : null;

                if ($period && $workunitid) {
                    // Extract year from period for comparison
                    $year = date('Y', strtotime($period));

                    // Check if combination already exists for current user
                    $existing = $this->beginningbalances->get(array(
                        'YEAR(period)' => $year,
                        'workunitid' => $workunitid,
                        'createdby' => getCurrentIdUser()
                    ));

                    if ($existing->num_rows() > 0) {
                        $this->db->trans_rollback();
                        return JSONResponseDefault('FAILED', 'Saldo awal untuk periode ' . $year . ' dan unit kerja ini sudah ada. Tidak dapat menambahkan duplikat.');
                    }
                }
            }

            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();

            $this->$models->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                $this->db->trans_rollback();

                return JSONResponseDefault('FAILED', 'Server sedang sibuk! Silahkan coba lagi nanti');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil ditambahkan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', 'Server sedang sibuk! Silahkan coba lagi nanti');
        }
    }

    public function edit($feature, $id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        // Load model with proper naming
        if ($feature == 'beginningbalance') {
            $this->load->model('Beginningbalances', 'beginningbalances');
            $models = 'beginningbalances';
        } else {
            $this->load->model(ucfirst($feature) . 's', strtolower($feature) . 's');
            $models = $feature . 's';
        }

        $get = $this->$models->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return redirect(base_url('master/' . $feature));
        }

        $data = array();
        $data['title'] = 'Ubah ' . ucwords($feature);
        $data['feature'] = $feature;
        $data['content'] = $this->crud->showContents($feature, 'edit');
        $data['element'] = $this->crud->getElements(getContents($feature, 'edit', $get->row()));

        return $this->load->view('master', $data);
    }

    public function process_edit($feature, $id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses.');
        }

        // Load model with proper naming
        if ($feature == 'beginningbalance') {
            $this->load->model('Beginningbalances', 'beginningbalances');
            $models = 'beginningbalances';
        } else {
            $this->load->model(ucfirst($feature) . 's', strtolower($feature) . 's');
            $models = $feature . 's';
        }

        $get = $this->$models->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $row = $get->row();

        $config = array();
        $config['upload_path'] = './uploads';
        $config['encrypt_name'] = true;
        $config['allowed_types'] = '*';

        $this->load->library('upload', $config);

        $update = array();
        foreach ($_FILES as $key => $value) {
            if ($value['name'] == null) continue;

            if ($this->upload->do_upload($key)) {
                $update[$key] = $this->upload->data('file_name');
            } else {
                return JSONResponseDefault('FAILED', $this->upload->display_errors('', ''));
            }
        }

        foreach ($_POST as $key => $value) {
            if (str_contains($key, 'validated')) {
                $col = explode('_', $key);

                if (count($col) == 2) {
                    if (getPost($key) != $row->{$col[1]}) {
                        $validate = $this->$models->get(array($col[1] => getPost($key)));

                        if ($validate->num_rows() > 0) {
                            return JSONResponseDefault('FAILED', ucwords($col[1]) . ' yang anda masukkan telah terdata');
                        }

                        $update[$col[1]] = getPost($key);
                    }
                } elseif (count($col) > 2) {
                    if ($col[0] == 'validated' && $col[1] == 'with') {
                        $columns = $col[2];
                        $table = $col[4];
                        $parameter = $col[5];
                        $statement = $col[6];
                        $options = $col[8];

                        $get = $this->db->get_where($table, array('id' => getPost($parameter)))->row();

                        switch ($statement) {
                            case 'mustlessthanorsame':
                                if ($get != null && $get->{$columns} < $value) {
                                    $message = ucwords($col[7]) . " harus kurang dari atau sama dengan jumlah $columns yang tersedia";

                                    return JSONResponseDefault('FAILED', $message);
                                } elseif ($get != null && $options == 'andthen' && $col[9] == 'update') {
                                    $upd = array();
                                    $upd[$columns] = $get->{$columns} - getPost($key);

                                    $this->db->set($upd)->where('id', $get->id)->update($table);
                                }

                                break;
                        }

                        $update[$col[7]] = getPost($key);
                    } else if ($col[0] == 'required' && $col[1] == 'validated') {
                        $columns = $col[2];

                        if (getPost($key) != $row->{$columns}) {
                            $validate = $this->$models->get(array($columns => getPost($key)));

                            if ($validate->num_rows() > 0) {
                                return JSONResponseDefault('FAILED', ucwords($columns) . ' yang anda masukkan telah terdata');
                            }

                            $update[$columns] = getPost($key);
                        }
                    }
                }
            } else if (str_contains($key, 'required')) {
                $col = explode('_', $key);

                array_shift($col);
                $columns = implode('_', $col);

                if (getPost($key) == null) {
                    return JSONResponseDefault('FAILED', ucwords($columns) . ' harus diisi');
                } else if (is_numeric(getPost($key))) {
                    if (getPost($key) < 0) {
                        return JSONResponseDefault('FAILED', ucwords($columns) . ' tidak boleh bernilai negatif');
                    }
                } else if (is_array(getPost($key))) {
                    if (count(getPost($key)) == 0) {
                        return JSONResponseDefault('FAILED', ucwords($columns) . ' harus dipilih');
                    }
                }

                if (str_contains($columns, 'validated')) {
                    $col = explode('_', $columns);

                    $update[$col[1]] = getPost($key);
                } else {
                    // Special handling for beginning balance period field
                    if ($feature == 'beginningbalance' && $columns == 'period') {
                        // Convert YYYY to YYYY-01-01 for database storage
                        $periodValue = getPost($key);
                        if (preg_match('/^\d{4}$/', $periodValue)) {
                            $update[$columns] = $periodValue . '-01-01';
                        } else {
                            $update[$columns] = $periodValue;
                        }
                    } else {
                        $update[$columns] = getPost($key);
                    }
                }
            } else {
                if (getPost($key) != null) {
                    if (str_contains($key, 'password')) {
                        $update[$key] = password_hash(getPost($key), PASSWORD_DEFAULT);
                    } else {
                        if (str_contains($key, 'validated')) {
                            $col = explode('_', $key);

                            $update[$col[1]] = getPost($key);
                        } else {
                            if (is_array(getPost($key))) {
                                $update[$key] = implode(',', getPost($key));
                            } else {
                                // Special handling for beginning balance period field
                                if ($feature == 'beginningbalance' && $key == 'period') {
                                    // Convert YYYY to YYYY-01-01 for database storage
                                    $periodValue = getPost($key);
                                    if (preg_match('/^\d{4}$/', $periodValue)) {
                                        $update[$key] = $periodValue . '-01-01';
                                    } else {
                                        $update[$key] = $periodValue;
                                    }
                                } else {
                                    $update[$key] = getPost($key);
                                }
                            }
                        }
                    }
                }
            }
        }

        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        $this->$models->update(array('id' => $id), $update);

        return JSONResponseDefault('OK', 'Data berhasil diubah');
    }

    public function process_delete($feature)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses.');
        }

        $id = getPost('id');

        $this->load->model(ucfirst($feature) . 's', strtolower($feature) . 's');
        $models = $feature . 's';

        $get = $this->$models->get(array('id' => $id));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        // Prevent super admin from deleting their own account
        if ($feature === 'superadmin' && $id == getCurrentIdUser()) {
            return JSONResponseDefault('FAILED', 'Anda tidak dapat menghapus akun Anda sendiri.');
        }

        $this->$models->delete(array('id' => $id));

        return JSONResponseDefault('OK', 'Data berhasil dihapus');
    }
}

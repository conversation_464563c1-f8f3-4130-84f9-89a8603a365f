<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
    <h6 class="fw-semibold mb-0">Manajemen Pengguna</h6>

    <ul class="d-flex align-items-center gap-2">
        <li class="fw-medium">
            <a href="<?= base_url() ?>" class="d-flex align-items-center gap-1 hover-text-primary">
                <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                Dashboard
            </a>
        </li>
        <li>-</li>
        <li class="fw-medium">Manajemen Pengguna</li>
    </ul>
</div>

<!-- Statistics Cards -->
<div class="row row-cols-xl-3 row-cols-lg-3 row-cols-sm-2 row-cols-1 gy-4 mb-24">
    <div class="col">
        <div class="card shadow-none border bg-gradient-start-1 h-100">
            <div class="card-body p-20">
                <div class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                    <div>
                        <p class="fw-medium text-primary-light mb-1">Total Pengguna</p>
                        <h6 class="mb-0"><?= $statistics['total_all'] ?></h6>
                    </div>
                    <div class="w-50-px h-50-px bg-cyan rounded-circle d-flex justify-content-center align-items-center">
                        <iconify-icon icon="solar:users-group-two-rounded-bold" class="text-white text-2xl mb-0"></iconify-icon>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col">
        <div class="card shadow-none border bg-gradient-start-2 h-100">
            <div class="card-body p-20">
                <div class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                    <div>
                        <p class="fw-medium text-primary-light mb-1">Pengguna Aktif</p>
                        <h6 class="mb-0"><?= $statistics['total_aktif'] ?></h6>
                    </div>
                    <div class="w-50-px h-50-px bg-success-main rounded-circle d-flex justify-content-center align-items-center">
                        <iconify-icon icon="solar:user-check-bold" class="text-white text-2xl mb-0"></iconify-icon>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col">
        <div class="card shadow-none border bg-gradient-start-3 h-100">
            <div class="card-body p-20">
                <div class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                    <div>
                        <p class="fw-medium text-primary-light mb-1">Pengguna Nonaktif</p>
                        <h6 class="mb-0"><?= $statistics['total_nonaktif'] ?></h6>
                    </div>
                    <div class="w-50-px h-50-px bg-warning-main rounded-circle d-flex justify-content-center align-items-center">
                        <iconify-icon icon="solar:user-cross-bold" class="text-white text-2xl mb-0"></iconify-icon>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card basic-data-table">
    <div class="card-header d-flex justify-content-between align-items-center">
        <div>
            <h5 class="card-title mb-0">Daftar Pengguna</h5>
        </div>

        <div class="d-flex gap-2">
            <a href="<?= base_url(uri_string() . '/export') ?>" class="btn btn-success btn-sm d-flex gap-1 align-items-center">
                <iconify-icon icon="solar:download-bold"></iconify-icon>
                <span>Export CSV</span>
            </a>

            <a href="<?= base_url(uri_string() . '/add') ?>" class="btn btn-primary btn-sm">
                <i class="ri-add-line"></i>
                <span>Tambah Pengguna</span>
            </a>
        </div>
    </div>

    <div class="card-body">
        <!-- Filter Section -->
        <div class="row mb-3">
            <div class="col-md-4">
                <label for="filter_workunit" class="form-label">Filter Unit Usaha</label>
                <select id="filter_workunit" class="form-select">
                    <option value="">Semua Unit Usaha</option>
                    <?php foreach ($workunits as $workunit): ?>
                        <option value="<?= $workunit->id ?>"><?= $workunit->workunitname ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table bordered-table datatables">
                <thead>
                    <tr>
                        <th>Nama</th>
                        <th>Username</th>
                        <th>No. WhatsApp</th>
                        <th>Unit Usaha</th>
                        <th>Status</th>
                        <th>Tanggal Dibuat</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($users as $user): ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="flex-grow-1">
                                        <h6 class="text-md mb-0 fw-medium"><?= $user->name ?></h6>
                                        <?php if (!empty($user->address)): ?>
                                            <span class="text-sm text-secondary-light fw-medium"><?= substr($user->address, 0, 50) ?><?= strlen($user->address) > 50 ? '...' : '' ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="text-md fw-medium"><?= $user->username ?></span>
                            </td>
                            <td>
                                <?php if (!empty($user->phone)): ?>
                                    <a href="https://wa.me/<?= preg_replace('/[^0-9]/', '', $user->phone) ?>" target="_blank" class="text-success-main">
                                        <?= $user->phone ?>
                                    </a>
                                <?php else: ?>
                                    <span class="text-secondary-light">-</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="text-md fw-medium"><?= $user->workunitname ?></span>
                            </td>
                            <td>
                                <?php if ($user->status == 'Aktif'): ?>
                                    <span class="bg-success-focus text-success-main px-24 py-4 rounded-pill fw-medium text-sm">Aktif</span>
                                <?php else: ?>
                                    <span class="bg-warning-focus text-warning-main px-24 py-4 rounded-pill fw-medium text-sm">Nonaktif</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="text-md fw-medium"><?= date('d/m/Y H:i', strtotime($user->createddate)) ?></span>
                            </td>
                            <td>
                                <div class="d-flex align-items-center gap-10">
                                    <a href="<?= base_url(uri_string() . '/edit/' . $user->id) ?>" class="bg-success-focus text-success-main bg-hover-success-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle">
                                        <iconify-icon icon="lucide:edit" class="menu-icon"></iconify-icon>
                                    </a>

                                    <button type="button" class="bg-warning-focus text-warning-main bg-hover-warning-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle border-0" onclick="toggleStatus(<?= $user->id ?>, '<?= $user->status == 'Aktif' ? 'Nonaktif' : 'Aktif' ?>')">
                                        <?php if ($user->status == 'Aktif'): ?>
                                            <iconify-icon icon="lucide:user-x" class="menu-icon"></iconify-icon>
                                        <?php else: ?>
                                            <iconify-icon icon="lucide:user-check" class="menu-icon"></iconify-icon>
                                        <?php endif; ?>
                                    </button>

                                    <button type="button" class="bg-danger-focus text-danger-main bg-hover-danger-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle border-0" onclick="deleteUser(<?= $user->id ?>, '<?= $user->name ?>')">
                                        <iconify-icon icon="lucide:trash-2" class="menu-icon"></iconify-icon>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
    // Base URL for AJAX calls
    var base_url = '<?= base_url() ?>';

    function toggleStatus(id, newStatus) {
        const statusText = newStatus === 'Aktif' ? 'mengaktifkan' : 'menonaktifkan';

        swal({
            title: 'Konfirmasi',
            text: `Apakah Anda yakin ingin ${statusText} pengguna ini?`,
            icon: 'warning',
            buttons: {
                cancel: {
                    text: 'Batal',
                    value: false,
                    visible: true,
                    className: 'btn btn-secondary',
                    closeModal: true,
                },
                confirm: {
                    text: 'Ya, Lanjutkan',
                    value: true,
                    visible: true,
                    className: 'btn btn-primary',
                    closeModal: true
                }
            }
        }).then((willProceed) => {
            if (willProceed) {
                $.ajax({
                    url: '<?= base_url('bumdes_users/toggle_status') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        id: id,
                        status: newStatus
                    },
                    success: function(response) {
                        if (response.RESULT === 'OK') {
                            swal({
                                title: 'Berhasil!',
                                text: response.MESSAGE,
                                icon: 'success',
                                button: 'OK'
                            }).then(() => {
                                window.location.reload();
                            });
                        } else {
                            swal({
                                title: 'Gagal!',
                                text: response.MESSAGE,
                                icon: 'error',
                                button: 'OK'
                            });
                        }
                    },
                    error: function() {
                        swal({
                            title: 'Error!',
                            text: 'Terjadi kesalahan sistem.',
                            icon: 'error',
                            button: 'OK'
                        });
                    }
                });
            }
        });
    }

    function deleteUser(id, name) {
        swal({
            title: 'Konfirmasi Hapus',
            text: `Apakah Anda yakin ingin menghapus pengguna "${name}"? Data yang sudah dihapus tidak dapat dikembalikan.`,
            icon: 'warning',
            buttons: {
                cancel: {
                    text: 'Batal',
                    value: false,
                    visible: true,
                    className: 'btn btn-secondary',
                    closeModal: true,
                },
                confirm: {
                    text: 'Ya, Hapus',
                    value: true,
                    visible: true,
                    className: 'btn btn-danger',
                    closeModal: true
                }
            },
            dangerMode: true
        }).then((willDelete) => {
            if (willDelete) {
                $.ajax({
                    url: '<?= base_url('bumdes_users/delete') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        id: id
                    },
                    success: function(response) {
                        if (response.RESULT === 'OK') {
                            swal({
                                title: 'Berhasil!',
                                text: response.MESSAGE,
                                icon: 'success',
                                button: 'OK'
                            }).then(() => {
                                window.location.reload();
                            });
                        } else {
                            swal({
                                title: 'Gagal!',
                                text: response.MESSAGE,
                                icon: 'error',
                                button: 'OK'
                            });
                        }
                    },
                    error: function() {
                        swal({
                            title: 'Error!',
                            text: 'Terjadi kesalahan sistem.',
                            icon: 'error',
                            button: 'OK'
                        });
                    }
                });
            }
        });
    }

    // Filter functionality
    window.onload = function() {
        var table = $('.datatables').DataTable();

        // Work unit filter
        $('#filter_workunit').on('change', function() {
            var workunitId = this.value;
            if (workunitId) {
                // Get the selected work unit text
                var workunitText = $(this).find('option:selected').text();
                // Filter by work unit name (column index 3 - Unit Usaha)
                table.column(3).search(workunitText, false, false).draw();
            } else {
                table.column(3).search('').draw();
            }
        });
    }
</script>

<!-- Include BUMDes Users JavaScript -->
<script src="<?= base_url('assets/js/bumdes-users.js') ?>"></script>
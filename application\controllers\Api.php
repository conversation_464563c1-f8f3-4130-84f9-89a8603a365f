<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Api extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('MsProvinces', 'msprovinces');
        $this->load->model('MsCities', 'mscities');
        $this->load->model('MsDistricts', 'msdistricts');
        $this->load->model('MsVillages', 'msvillages');
    }

    public function get_cities_by_province()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses.');
        }

        $province_code = getPost('province_code');
        
        if (empty($province_code)) {
            return JSONResponseDefault('FAILED', 'Kode provinsi tidak boleh kosong.');
        }

        $cities = $this->mscities->select('code, name')
            ->where('province_code', $province_code)
            ->order_by('name', 'ASC')
            ->get()
            ->result();

        return JSONResponse([
            'RESULT' => 'OK',
            'MESSAGE' => 'Data berhasil diambil',
            'DATA' => $cities
        ]);
    }

    public function get_districts_by_city()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses.');
        }

        $city_code = getPost('city_code');
        
        if (empty($city_code)) {
            return JSONResponseDefault('FAILED', 'Kode kab/kota tidak boleh kosong.');
        }

        $districts = $this->msdistricts->select('code, name')
            ->where('city_code', $city_code)
            ->order_by('name', 'ASC')
            ->get()
            ->result();

        return JSONResponse([
            'RESULT' => 'OK',
            'MESSAGE' => 'Data berhasil diambil',
            'DATA' => $districts
        ]);
    }

    public function get_villages_by_district()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses.');
        }

        $district_code = getPost('district_code');
        
        if (empty($district_code)) {
            return JSONResponseDefault('FAILED', 'Kode kecamatan tidak boleh kosong.');
        }

        $villages = $this->msvillages->select('code, name')
            ->where('district_code', $district_code)
            ->order_by('name', 'ASC')
            ->get()
            ->result();

        return JSONResponse([
            'RESULT' => 'OK',
            'MESSAGE' => 'Data berhasil diambil',
            'DATA' => $villages
        ]);
    }

    public function get_provinces()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses.');
        }

        $provinces = $this->msprovinces->select('code, name')
            ->order_by('name', 'ASC')
            ->get()
            ->result();

        return JSONResponse([
            'RESULT' => 'OK',
            'MESSAGE' => 'Data berhasil diambil',
            'DATA' => $provinces
        ]);
    }
}

<?php
defined('BASEPATH') or die('No direct script access allowed!');

class MsVillages extends MY_Model
{
    protected $table = 'msvillages';

    public function insert($data)
    {
        // Auto-generate village code based on district code
        $districtCode = $data['district_code'];

        $lastCode = $this->db->select_max('code')
            ->from($this->table)
            ->where('district_code', $districtCode)
            ->get()
            ->row();

        if ($lastCode && $lastCode->code) {
            // Extract the last 3 digits and increment
            $lastNumber = (int)substr($lastCode->code, -3);
            $newNumber = str_pad($lastNumber + 1, 3, '0', STR_PAD_LEFT);
            $newCode = $districtCode . $newNumber;
        } else {
            $newCode = $districtCode . '001';
        }

        $data['code'] = $newCode;
        $data['createddate'] = getCurrentDate();
        $data['createdby'] = getCurrentIdUser();

        return parent::insert($data);
    }
}

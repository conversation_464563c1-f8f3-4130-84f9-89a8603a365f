<?php
defined('BASEPATH') or die('No direct script access allowed!');

class MsProvinces extends MY_Model
{
    protected $table = 'msprovinces';

    public function insert($data = [])
    {
        // Auto-generate province code
        $lastCode = $this->db->select_max('code')
            ->from($this->table)
            ->get()
            ->row();

        if ($lastCode && $lastCode->code) {
            $newCode = str_pad((int)$lastCode->code + 1, 2, '0', STR_PAD_LEFT);
        } else {
            $newCode = '01';
        }

        $data['code'] = $newCode;
        $data['createddate'] = getCurrentDate();
        $data['createdby'] = getCurrentIdUser();

        return parent::insert($data);
    }
}

<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<!-- meta tags and other links -->
<!DOCTYPE html>
<html lang="en" data-theme="light">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kelolakas - <?= $element['title'] ?? ($title ?? 'Default Title') ?></title>
    <link rel="icon" type="image/png" href="<?= base_url('wowdash') ?>/images/favicon.png" sizes="16x16">

    <!-- remix icon font css  -->
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/remixicon.css">

    <!-- BootStrap css -->
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/lib/bootstrap.min.css">

    <!-- Apex Chart css -->
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/lib/apexcharts.css">

    <!-- Data Table css -->
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/lib/dataTables.min.css">

    <!-- Text Editor css -->
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/lib/editor-katex.min.css">
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/lib/editor.atom-one-dark.min.css">
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/lib/editor.quill.snow.css">

    <!-- Date picker css -->
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/lib/flatpickr.min.css">

    <!-- Calendar css -->
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/lib/full-calendar.css">

    <!-- Vector Map css -->
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/lib/jquery-jvectormap-2.0.5.css">

    <!-- Popup css -->
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/lib/magnific-popup.css">

    <!-- Slick Slider css -->
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/lib/slick.css">

    <!-- prism css -->
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/lib/prism.css">

    <!-- file upload css -->
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/lib/file-upload.css">
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/lib/audioplayer.css">

    <!-- select2 -->
    <link rel="stylesheet" href="<?= base_url() ?>node_modules/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?= base_url() ?>node_modules/bootstrap-multiselect-1.1.2/dist/css/bootstrap-multiselect.min.css">

    <!-- iziToast -->
    <link rel="stylesheet" href="<?= base_url() ?>node_modules/izitoast/dist/css/iziToast.min.css">

    <!-- main css -->
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/style.css">

    <style>
        .select2 .selection,
        table.dataTable {
            width: 100% !important;
        }
    </style>
</head>

<body>
    <aside class="sidebar">
        <button type="button" class="sidebar-close-btn">
            <iconify-icon icon="radix-icons:cross-2"></iconify-icon>
        </button>

        <div>
            <a href="<?= base_url() ?>" class="sidebar-logo">
                <h6 class="text-3xl font-bold text-primary">Kelolakas.id</h6>
            </a>
        </div>

        <div class="sidebar-menu-area">
            <ul class="sidebar-menu" id="sidebar-menu">
                <li class="sidebar-menu-group-title">Dashboard</li>
                <li>
                    <a href="<?= base_url('dashboard') ?>">
                        <iconify-icon icon="solar:home-smile-angle-outline" class="menu-icon"></iconify-icon>
                        <span>Dashboard</span>
                    </a>
                </li>

                <li class="sidebar-menu-group-title">Manajemen</li>
                <?php if (isSuperAdmin()): ?>
                    <li>
                        <a href="<?= base_url('master/superadmin') ?>">
                            <iconify-icon icon="carbon:user-role" class="menu-icon"></iconify-icon>
                            <span>Super Admin</span>
                        </a>
                    </li>
                    <li>
                        <a href="<?= base_url('master/workunit') ?>">
                            <iconify-icon icon="carbon:user-role" class="menu-icon"></iconify-icon>
                            <span>Unit Usaha</span>
                        </a>
                    </li>
                    <li>
                        <a href="<?= base_url('master/bumdes') ?>">
                            <iconify-icon icon="carbon:user-role" class="menu-icon"></iconify-icon>
                            <span>BUMDes</span>
                        </a>
                    </li>
                    <li>
                        <a href="<?= base_url('transaction_history') ?>">
                            <iconify-icon icon="carbon:time" class="menu-icon"></iconify-icon>
                            <span>History Status Transaksi</span>
                        </a>
                    </li>

                    <li class="sidebar-menu-group-title">Data Wilayah</li>
                    <li>
                        <a href="<?= base_url('master/msprovince') ?>">
                            <iconify-icon icon="carbon:location" class="menu-icon"></iconify-icon>
                            <span>Provinsi</span>
                        </a>
                    </li>
                    <li>
                        <a href="<?= base_url('master/mscity') ?>">
                            <iconify-icon icon="carbon:location" class="menu-icon"></iconify-icon>
                            <span>Kab/Kota</span>
                        </a>
                    </li>
                    <li>
                        <a href="<?= base_url('master/msdistrict') ?>">
                            <iconify-icon icon="carbon:location" class="menu-icon"></iconify-icon>
                            <span>Kecamatan</span>
                        </a>
                    </li>
                    <li>
                        <a href="<?= base_url('master/msvillage') ?>">
                            <iconify-icon icon="carbon:location" class="menu-icon"></iconify-icon>
                            <span>Desa</span>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if (isBumdes()): ?>
                    <!-- Menu untuk BUMDes -->
                    <li>
                        <a href="<?= base_url('bumdes_users') ?>">
                            <iconify-icon icon="carbon:user-multiple" class="menu-icon"></iconify-icon>
                            <span>Manajemen Pengguna</span>
                        </a>
                    </li>
                    <li>
                        <a href="<?= base_url('master/customer') ?>">
                            <iconify-icon icon="carbon:user" class="menu-icon"></iconify-icon>
                            <span>Pelanggan</span>
                        </a>
                    </li>

                    <li>
                        <a href="<?= base_url('master/beginningbalance') ?>">
                            <iconify-icon icon="carbon:currency-dollar" class="menu-icon"></iconify-icon>
                            <span>Saldo Awal</span>
                        </a>
                    </li>
                    <li>
                        <a href="<?= base_url('master/transaction') ?>">
                            <iconify-icon icon="carbon:shopping-cart" class="menu-icon"></iconify-icon>
                            <span>Transaksi</span>
                        </a>
                    </li>
                    <li>
                        <a href="<?= base_url('transaction_history') ?>">
                            <iconify-icon icon="carbon:time" class="menu-icon"></iconify-icon>
                            <span>History Status Transaksi</span>
                        </a>
                    </li>


                    <li class="sidebar-menu-group-title">Laporan Transaksi</li>

                    <li>
                        <a href="<?= base_url('report/transaction/daily') ?>">
                            <iconify-icon icon="carbon:calendar" class="menu-icon"></iconify-icon>
                            <span>Harian</span>
                        </a>
                    </li>
                    <li>
                        <a href="<?= base_url('report/transaction/monthly') ?>">
                            <iconify-icon icon="carbon:calendar" class="menu-icon"></iconify-icon>
                            <span>Bulanan</span>
                        </a>
                    </li>

                    <li class="sidebar-menu-group-title">Laporan Keuangan</li>
                    <li>
                        <a href="<?= base_url('report/balance') ?>">
                            <iconify-icon icon="carbon:currency" class="menu-icon"></iconify-icon>
                            <span>Saldo</span>
                        </a>
                    </li>

                    <!-- Laporan BUMDes - Temporarily disabled -->
                    <?php /*
                    <li class="sidebar-menu-group-title">Laporan BUMDes</li>
                    <li>
                        <a href="<?= base_url('bumdes_report') ?>">
                            <iconify-icon icon="carbon:report" class="menu-icon"></iconify-icon>
                            <span>Laporan BUMDes</span>
                        </a>
                    </li>
                    */ ?>
                <?php endif; ?>

                <?php if (isBumdesUser()): ?>
                    <!-- Menu untuk Pengguna BUMDes -->
                    <li class="sidebar-menu-group-title">Transaksi</li>
                    <li>
                        <a href="<?= base_url('master/transaction') ?>">
                            <iconify-icon icon="carbon:shopping-cart" class="menu-icon"></iconify-icon>
                            <span>Kelola Transaksi</span>
                        </a>
                    </li>
                    <li>
                        <a href="<?= base_url('transaction_history') ?>">
                            <iconify-icon icon="carbon:time" class="menu-icon"></iconify-icon>
                            <span>History Status Transaksi</span>
                        </a>
                    </li>

                    <li class="sidebar-menu-group-title">Laporan</li>
                    <li>
                        <a href="<?= base_url('report/transaction/daily') ?>">
                            <iconify-icon icon="carbon:calendar" class="menu-icon"></iconify-icon>
                            <span>Laporan Harian</span>
                        </a>
                    </li>
                    <li>
                        <a href="<?= base_url('report/transaction/monthly') ?>">
                            <iconify-icon icon="carbon:calendar" class="menu-icon"></iconify-icon>
                            <span>Laporan Bulanan</span>
                        </a>
                    </li>
                <?php endif; ?>

                <!-- <?php if (isSuperAdmin()): ?>
                    <li class="sidebar-menu-group-title">Laporan BUMDes</li>
                    <li>
                        <a href="<?= base_url('bumdes_report') ?>">
                            <iconify-icon icon="carbon:report" class="menu-icon"></iconify-icon>
                            <span>Monitoring Laporan</span>
                        </a>
                    </li>
                <?php endif; ?> -->
            </ul>
        </div>
    </aside>

    <main class="dashboard-main">
        <div class="navbar-header">
            <div class="row align-items-center justify-content-between">
                <div class="col-auto">
                    <div class="d-flex flex-wrap align-items-center gap-4">
                        <button type="button" class="sidebar-toggle">
                            <iconify-icon icon="heroicons:bars-3-solid" class="icon text-2xl non-active"></iconify-icon>
                            <iconify-icon icon="iconoir:arrow-right" class="icon text-2xl active"></iconify-icon>
                        </button>
                        <button type="button" class="sidebar-mobile-toggle">
                            <iconify-icon icon="heroicons:bars-3-solid" class="icon"></iconify-icon>
                        </button>
                        <form class="navbar-search">
                            <input type="text" name="search" placeholder="Search">
                            <iconify-icon icon="ion:search-outline" class="icon"></iconify-icon>
                        </form>
                    </div>
                </div>
                <div class="col-auto">
                    <div class="d-flex flex-wrap align-items-center gap-3">
                        <!-- Year Filter -->
                        <div class="d-flex align-items-center gap-2">
                            <label for="year-filter" class="text-sm fw-medium text-secondary-light">Tahun:</label>
                            <select id="year-filter" class="form-select form-select-sm" style="width: 80px;">
                                <?php
                                $currentYear = date('Y');
                                $selectedYear = getSelectedYear();
                                for ($year = $currentYear - 2; $year <= $currentYear + 1; $year++): ?>
                                    <option value="<?= $year ?>" <?= $year == $selectedYear ? 'selected' : '' ?>><?= $year ?></option>
                                <?php endfor; ?>
                            </select>
                        </div>

                        <button type="button" data-theme-toggle class="w-40-px h-40-px bg-neutral-200 rounded-circle d-flex justify-content-center align-items-center"></button>

                        <div class="dropdown">
                            <button class="has-indicator w-40-px h-40-px bg-neutral-200 rounded-circle d-flex justify-content-center align-items-center" type="button" data-bs-toggle="dropdown">
                                <iconify-icon icon="iconoir:bell" class="text-primary-light text-xl"></iconify-icon>
                            </button>
                            <div class="dropdown-menu to-top dropdown-menu-lg p-0">
                                <div class="m-16 py-12 px-16 radius-8 bg-primary-50 mb-16 d-flex align-items-center justify-content-between gap-2">
                                    <div>
                                        <h6 class="text-lg text-primary-light fw-semibold mb-0">Notifications</h6>
                                    </div>
                                    <span class="text-primary-600 fw-semibold text-lg w-40-px h-40-px rounded-circle bg-base d-flex justify-content-center align-items-center">05</span>
                                </div>

                                <div class="max-h-400-px overflow-y-auto scroll-sm pe-4">
                                    <a href="javascript:void(0)" class="px-24 py-12 d-flex align-items-start gap-3 mb-2 justify-content-between">
                                        <div class="text-black hover-bg-transparent hover-text-primary d-flex align-items-center gap-3">
                                            <span class="w-44-px h-44-px bg-success-subtle text-success-main rounded-circle d-flex justify-content-center align-items-center flex-shrink-0">
                                                <iconify-icon icon="bitcoin-icons:verify-outline" class="icon text-xxl"></iconify-icon>
                                            </span>
                                            <div>
                                                <h6 class="text-md fw-semibold mb-4">Congratulations</h6>
                                                <p class="mb-0 text-sm text-secondary-light text-w-200-px">Your profile has been Verified. Your profile has been Verified</p>
                                            </div>
                                        </div>
                                        <span class="text-sm text-secondary-light flex-shrink-0">23 Mins ago</span>
                                    </a>

                                    <a href="javascript:void(0)" class="px-24 py-12 d-flex align-items-start gap-3 mb-2 justify-content-between bg-neutral-50">
                                        <div class="text-black hover-bg-transparent hover-text-primary d-flex align-items-center gap-3">
                                            <span class="w-44-px h-44-px bg-success-subtle text-success-main rounded-circle d-flex justify-content-center align-items-center flex-shrink-0">
                                                <img src="<?= base_url('wowdash') ?>/images/notification/profile-1.png" alt="">
                                            </span>
                                            <div>
                                                <h6 class="text-md fw-semibold mb-4">Ronald Richards</h6>
                                                <p class="mb-0 text-sm text-secondary-light text-w-200-px">You can stitch between artboards</p>
                                            </div>
                                        </div>
                                        <span class="text-sm text-secondary-light flex-shrink-0">23 Mins ago</span>
                                    </a>

                                    <a href="javascript:void(0)" class="px-24 py-12 d-flex align-items-start gap-3 mb-2 justify-content-between">
                                        <div class="text-black hover-bg-transparent hover-text-primary d-flex align-items-center gap-3">
                                            <span class="w-44-px h-44-px bg-info-subtle text-info-main rounded-circle d-flex justify-content-center align-items-center flex-shrink-0">
                                                AM
                                            </span>
                                            <div>
                                                <h6 class="text-md fw-semibold mb-4">Arlene McCoy</h6>
                                                <p class="mb-0 text-sm text-secondary-light text-w-200-px">Invite you to prototyping</p>
                                            </div>
                                        </div>
                                        <span class="text-sm text-secondary-light flex-shrink-0">23 Mins ago</span>
                                    </a>

                                    <a href="javascript:void(0)" class="px-24 py-12 d-flex align-items-start gap-3 mb-2 justify-content-between bg-neutral-50">
                                        <div class="text-black hover-bg-transparent hover-text-primary d-flex align-items-center gap-3">
                                            <span class="w-44-px h-44-px bg-success-subtle text-success-main rounded-circle d-flex justify-content-center align-items-center flex-shrink-0">
                                                <img src="<?= base_url('wowdash') ?>/images/notification/profile-2.png" alt="">
                                            </span>
                                            <div>
                                                <h6 class="text-md fw-semibold mb-4">Annette Black</h6>
                                                <p class="mb-0 text-sm text-secondary-light text-w-200-px">Invite you to prototyping</p>
                                            </div>
                                        </div>
                                        <span class="text-sm text-secondary-light flex-shrink-0">23 Mins ago</span>
                                    </a>

                                    <a href="javascript:void(0)" class="px-24 py-12 d-flex align-items-start gap-3 mb-2 justify-content-between">
                                        <div class="text-black hover-bg-transparent hover-text-primary d-flex align-items-center gap-3">
                                            <span class="w-44-px h-44-px bg-info-subtle text-info-main rounded-circle d-flex justify-content-center align-items-center flex-shrink-0">
                                                DR
                                            </span>
                                            <div>
                                                <h6 class="text-md fw-semibold mb-4">Darlene Robertson</h6>
                                                <p class="mb-0 text-sm text-secondary-light text-w-200-px">Invite you to prototyping</p>
                                            </div>
                                        </div>
                                        <span class="text-sm text-secondary-light flex-shrink-0">23 Mins ago</span>
                                    </a>
                                </div>

                                <div class="text-center py-12 px-16">
                                    <a href="javascript:void(0)" class="text-primary-600 fw-semibold text-md">See All Notification</a>
                                </div>

                            </div>
                        </div><!-- Notification dropdown end -->

                        <div class="dropdown">
                            <button class="d-flex justify-content-center align-items-center rounded-circle" type="button" data-bs-toggle="dropdown">
                                <img src="<?= base_url('wowdash') ?>/images/user.png" alt="image" class="w-40-px h-40-px object-fit-cover rounded-circle">
                            </button>
                            <div class="dropdown-menu to-top dropdown-menu-sm">
                                <div class="py-12 px-16 radius-8 bg-primary-50 mb-16 d-flex align-items-center justify-content-between gap-2">
                                    <div>
                                        <h6 class="text-lg text-primary-light fw-semibold mb-2"><?= getSessionValue('NAME') ?></h6>
                                        <span class="text-secondary-light fw-medium text-sm"><?= getSessionValue('ROLE') ?></span>
                                    </div>
                                    <button type="button" class="hover-text-danger">
                                        <iconify-icon icon="radix-icons:cross-1" class="icon text-xl"></iconify-icon>
                                    </button>
                                </div>
                                <ul class="to-top-list">
                                    <li>
                                        <a class="dropdown-item text-black px-0 py-8 hover-bg-transparent hover-text-primary d-flex align-items-center gap-3" href="<?= base_url('profile') ?>">
                                            <iconify-icon icon="solar:user-linear" class="icon text-xl"></iconify-icon> Profil Saya</a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item text-black px-0 py-8 hover-bg-transparent hover-text-primary d-flex align-items-center gap-3" href="<?= base_url('profile/change_password') ?>">
                                            <iconify-icon icon="solar:lock-password-outline" class="icon text-xl"></iconify-icon> Ubah Password</a>
                                    </li>
                                    <li>
                                        <hr class="dropdown-divider my-8">
                                    </li>
                                    <li>
                                        <a class="dropdown-item text-black px-0 py-8 hover-bg-transparent hover-text-danger d-flex align-items-center gap-3" href="javascript:;" onclick="doLogout()">
                                            <iconify-icon icon="lucide:power" class="icon text-xl"></iconify-icon>
                                            Log Out
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div><!-- Profile dropdown end -->
                    </div>
                </div>
            </div>
        </div>

        <div class="dashboard-main-body">
            <?php if (isset($content)): ?>
                <?php $this->load->view($content); ?>
            <?php endif; ?>
        </div>

        <footer class="d-footer">
            <div class="row align-items-center justify-content-between">
                <div class="col-auto">
                    <p class="mb-0">© 2024 Kelolakas.id. All Rights Reserved.</p>
                </div>
                <div class="col-auto">
                    <p class="mb-0">Made by <span class="text-primary-600">wowtheme7</span></p>
                </div>
            </div>
        </footer>
    </main>

    <div class="modal fade" tabindex="-1" role="dialog" id="ModalGlobal">
    </div>

    <!-- jQuery library js -->
    <script src="<?= base_url('wowdash') ?>/js/lib/jquery-3.7.1.min.js"></script>

    <!-- Bootstrap js -->
    <script src="<?= base_url('wowdash') ?>/js/lib/bootstrap.bundle.min.js"></script>

    <!-- Apex Chart js -->
    <script>
        // Global error handler for ApexCharts
        window.onerror = function(msg, url, lineNo, columnNo, error) {
            if (msg.indexOf('ApexCharts is not defined') !== -1) {
                console.warn('ApexCharts error caught and handled:', msg);
                return true; // Prevent default error handling
            }
            return false;
        };

        // Create a comprehensive ApexCharts placeholder
        (function() {
            var ApexChartsPlaceholder = function(element, options) {
                console.warn('ApexCharts called before library loaded, queuing for later execution');
                window.ApexChartsQueue = window.ApexChartsQueue || [];
                window.ApexChartsQueue.push({
                    element: element,
                    options: options,
                    timestamp: Date.now()
                });
                return {
                    render: function() {
                        console.warn('ApexCharts: Chart queued, will render after library loads');
                        return Promise.resolve();
                    },
                    destroy: function() {
                        return this;
                    },
                    updateOptions: function() {
                        return this;
                    },
                    updateSeries: function() {
                        return this;
                    },
                    appendData: function() {
                        return this;
                    },
                    toggleSeries: function() {
                        return this;
                    },
                    showSeries: function() {
                        return this;
                    },
                    hideSeries: function() {
                        return this;
                    },
                    resetSeries: function() {
                        return this;
                    },
                    zoomX: function() {
                        return this;
                    },
                    toggleDataPointSelection: function() {
                        return this;
                    },
                    appendSeries: function() {
                        return this;
                    },
                    addXaxisAnnotation: function() {
                        return this;
                    },
                    addYaxisAnnotation: function() {
                        return this;
                    },
                    addPointAnnotation: function() {
                        return this;
                    },
                    removeAnnotation: function() {
                        return this;
                    },
                    clearAnnotations: function() {
                        return this;
                    },
                    dataURI: function() {
                        return Promise.resolve('');
                    }
                };
            };

            // Set up the placeholder immediately
            window.ApexCharts = ApexChartsPlaceholder;

            // Also handle any existing references
            if (typeof ApexCharts === 'undefined') {
                window.ApexCharts = ApexChartsPlaceholder;
            }
        })();
    </script>
    <script src="<?= base_url('wowdash') ?>/js/lib/apexcharts.min.js"></script>
    <script>
        // After ApexCharts loads, process any queued charts
        setTimeout(function() {
            if (window.ApexChartsQueue && window.ApexChartsQueue.length > 0) {
                console.log('Processing ' + window.ApexChartsQueue.length + ' queued ApexCharts');
                window.ApexChartsQueue.forEach(function(item) {
                    try {
                        var element = item.element;
                        if (element && typeof element === 'string') {
                            element = document.querySelector(element);
                        }
                        if (element) {
                            // Check if chart already exists in this element
                            if (element.querySelector('.apexcharts-canvas')) {
                                console.log('ApexCharts: Chart already exists in element, skipping...');
                                return;
                            }
                            var chart = new ApexCharts(element, item.options);
                            chart.render();
                        } else {
                            console.warn('ApexCharts: Element not found for queued chart');
                        }
                    } catch (e) {
                        console.error('Error processing queued ApexCharts:', e);
                    }
                });
                window.ApexChartsQueue = [];
            }
        }, 100);
    </script>

    <!-- Data Table js -->
    <script src="<?= base_url('wowdash') ?>/js/lib/dataTables.min.js"></script>

    <!-- Iconify Font js -->
    <script src="<?= base_url('wowdash') ?>/js/lib/iconify-icon.min.js"></script>

    <!-- jQuery UI js -->
    <script src="<?= base_url('wowdash') ?>/js/lib/jquery-ui.min.js"></script>

    <!-- Vector Map js -->
    <script src="<?= base_url('wowdash') ?>/js/lib/jquery-jvectormap-2.0.5.min.js"></script>
    <script src="<?= base_url('wowdash') ?>/js/lib/jquery-jvectormap-world-mill-en.js"></script>

    <!-- Popup js -->
    <script src="<?= base_url('wowdash') ?>/js/lib/magnifc-popup.min.js"></script>

    <!-- Slick Slider js -->
    <script src="<?= base_url('wowdash') ?>/js/lib/slick.min.js"></script>

    <!-- prism js -->
    <script src="<?= base_url('wowdash') ?>/js/lib/prism.js"></script>

    <!-- file upload js -->
    <script src="<?= base_url('wowdash') ?>/js/lib/file-upload.js"></script>

    <!-- audioplayer -->
    <script src="<?= base_url('wowdash') ?>/js/lib/audioplayer.js"></script>

    <!-- select2 -->
    <script src="<?= base_url() ?>node_modules/select2/dist/js/select2.full.min.js"></script>

    <!-- iziToast -->
    <script src="<?= base_url() ?>node_modules/izitoast/dist/js/iziToast.min.js"></script>

    <!-- main js -->
    <script src="<?= base_url('wowdash') ?>/js/app.js"></script>

    <!-- helpers -->
    <script src="<?= base_url() ?>node_modules/sweetalert/dist/sweetalert.min.js"></script>
    <script src="<?= base_url() ?>node_modules/bootstrap-multiselect-1.1.2/dist/js/bootstrap-multiselect.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js" integrity="sha512-ElRFoEQdI5Ht6kZvyzXhYG9NqjtkmlkfYk0wr6wHxU9JEHakS7UJZNeml5ALk+8IKlU6jDgMabC3vkumRokgJA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="<?= base_url('assets/js/ajax-request.js') ?>"></script>
    <script src="<?= base_url('assets/js/script.js') ?>"></script>
    <script src="<?= base_url('wowdash/js/custom.js') ?>"></script>

    <script>
        // Initialize DataTables only for tables that don't have specific initialization
        $(document).ready(function() {
            // Only initialize .datatables that haven't been initialized yet
            $('.datatables').each(function() {
                if (!$.fn.DataTable.isDataTable(this)) {
                    $(this).DataTable({
                        ordering: false,
                        responsive: true,
                        pageLength: 10
                    });
                }
            });
        });

        // Debug: Check if ApexCharts is loaded
        if (typeof ApexCharts === 'undefined') {
            console.error('ApexCharts is not loaded. Please check the script inclusion.');
        } else {
            console.log('ApexCharts loaded successfully');
        }

        // Additional error handling for any remaining ApexCharts issues
        window.addEventListener('error', function(e) {
            if (e.message && e.message.indexOf('ApexCharts is not defined') !== -1) {
                console.warn('Caught ApexCharts error via event listener:', e.message);
                e.preventDefault();
                return false;
            }
        });

        // Override any remaining undefined ApexCharts references
        setTimeout(function() {
            if (typeof window.ApexCharts === 'undefined') {
                console.warn('ApexCharts still undefined, creating emergency placeholder');
                window.ApexCharts = function() {
                    console.warn('Emergency ApexCharts placeholder called');
                    return {
                        render: function() {
                            console.warn('Emergency placeholder render called');
                        },
                        destroy: function() {},
                        updateOptions: function() {},
                        updateSeries: function() {}
                    };
                };
            }
        }, 50);

        function togglePasswordVisibility(button_elm) {
            // find input field
            var input_field = $(button_elm).closest('.input-group').find('input');

            // toggle password visibility
            if (input_field.attr('type') == 'password') {
                input_field.attr('type', 'text');
                $(button_elm).find('i').removeClass('ri-eye-line').addClass('ri-eye-off-line');
            } else {
                input_field.attr('type', 'password');
                $(button_elm).find('i').removeClass('ri-eye-off-line').addClass('ri-eye-line');
            }
        }

        $.AjaxRequest('#frm', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return swalMessageSuccess(response.MESSAGE, ok => {
                        return window.location.href = $('#frm').attr('success-redirect');
                    })
                } else {
                    return swalMessageFailed(response.MESSAGE);
                }
            },
            error: function() {
                return swalError();
            }
        });

        function deleteData(nama, id) {
            swal({
                title: 'Apakah anda yakin?',
                text: `${nama} akan dihapus secara permanen dari database`,
                icon: 'warning',
                buttons: true,
                dangerMode: true
            }).then(isAccepted => {
                if (isAccepted) {
                    $.ajax({
                        url: '<?= base_url(uri_string() . '/delete') ?>',
                        method: 'POST',
                        dataType: 'json',
                        data: {
                            id: id
                        },
                        success: function(response) {
                            if (response.RESULT == 'OK') {
                                return swalMessageSuccess(response.MESSAGE, ok => {
                                    return window.location.reload();
                                });
                            } else {
                                return swalMessageFailed(response.MESSAGE);
                            }
                        }
                    }).fail(function() {
                        return swalError();
                    })
                }
            })
        }

        function doLogout() {
            swal({
                title: 'Apakah anda yakin?',
                text: `Apakah anda yakin ingin keluar dari aplikasi?`,
                icon: 'warning',
                buttons: true,
                dangerMode: true
            }).then(isAccepted => {
                if (isAccepted) {
                    return window.location.href = '<?= base_url('auth/logout') ?>';
                }
            });
        }
    </script>

    <!-- Location Management JavaScript for cascading dropdowns -->
    <script src="<?= base_url('assets/js/location-management.js') ?>"></script>
</body>

</html>
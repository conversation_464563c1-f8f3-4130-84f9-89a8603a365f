<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
    <h6 class="fw-semibold mb-0"><?= isset($element['title']) ? $element['title'] : 'Data ' . $title ?></h6>

    <ul class="d-flex align-items-center gap-2">
        <li class="fw-medium">
            <a href="<?= base_url() ?>" class="d-flex align-items-center gap-1 hover-text-primary">
                <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                Dashboard
            </a>
        </li>
        <li>-</li>
        <li class="fw-medium"><?= isset($element['title']) ? $element['title'] : 'Data ' . $title ?></li>
    </ul>
</div>

<div class="card basic-data-table">
    <div class="card-header d-flex justify-content-between align-items-center">
        <div>
            <h5 class="card-title mb-0"><?= isset($element['title']) ? $element['title'] : 'Data ' . $title ?></h5>
        </div>

        <div>
            <?php if (!isset($element['buttons'])) : ?>
                <a href="<?= base_url(uri_string() . '/add') ?>" class="btn btn-primary btn-sm">
                    <i class="ri-add-line"></i>
                    <span>Tambah</span>
                </a>
            <?php else : ?>
                <?php foreach ($element['buttons'] as $key => $value) : ?>
                    <?php
                    $attr = isset($value['attr']) ? $value['attr'] : array();

                    $attribute = "";
                    foreach ($attr as $k => $v) {
                        $attribute .= "$k=\"$v\" ";
                    }
                    ?>

                    <?php if ($value['type'] == 'a') : ?>
                        <a href="<?= $value['href'] ?>" class="<?= $value['class'] ?>" <?= $attribute ?>>
                            <?php if (isset($value['icon'])) : ?>
                                <i class="<?= $value['icon'] ?>"></i>
                            <?php endif; ?>

                            <span><?= $value['text'] ?></span>
                        </a>
                    <?php elseif ($value['type'] == 'button') : ?>
                        <button type="button" class="<?= $value['class'] ?>" <?= $attribute ?>>
                            <?php if (isset($value['icon'])) : ?>
                                <i class="<?= $value['icon'] ?>"></i>
                            <?php endif; ?>

                            <span><?= $value['text'] ?></span>
                        </button>
                    <?php elseif ($value['type'] == 'span') : ?>
                        <span><?= $value['text'] ?></span>
                    <?php endif; ?>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <div class="card-body">
        <?php if (isset($element['filters'])) : ?>
            <form action="<?= $element['filters']['form_action'] ?>" method="<?= $element['filters']['form_action'] ?>">
                <div class="row">
                    <?php foreach ($element['filters']['data'] as $key => $value) : ?>
                        <?php
                        $attr = isset($value['attr']) ? $value['attr'] : array();

                        $attribute = "";
                        foreach ($attr as $k => $v) {
                            $attribute .= "$k='$v' ";
                        }
                        ?>

                        <div class="<?= $value['parent_class'] ?>">
                            <div class="<?= isset($value['input_type']) ? ($value['input_type'] == 'hidden' ? 'd-none' : 'mb-3') : 'mb-3' ?>">
                                <?php if (isset($value['label'])) : ?>
                                    <label for=""><?= $value['label'] ?> <?= isset($value['required']) ? '<span class="text-danger">*</span>' : null ?></label>
                                <?php endif; ?>

                                <?php if (isset($value['type'])) : ?>
                                    <?php if ($value['type'] == 'input') : ?>
                                        <input type="<?= $value['input_type'] ?>" name="<?= $value['variable'] ?>" class="form-control" placeholder="<?= isset($value['placeholder']) ? $value['placeholder'] : null ?>" value="<?= isset($value['default']) ? $value['default'] : null ?>" <?= isset($value['required']) ? 'required' : null ?> <?= $attribute ?>>
                                    <?php elseif ($value['type'] == 'textarea') : ?>
                                        <textarea name="<?= $value['variable'] ?>" class="form-control" placeholder="<?= $value['placeholder'] ?>" <?= isset($value['required']) ? 'required' : null ?> <?= $attribute ?>></textarea>
                                    <?php elseif ($value['type'] == 'select') : ?>
                                        <select name="<?= $value['variable'] ?>" class="form-control" <?= isset($value['required']) ? 'required' : null ?> <?= $attribute ?>>
                                            <?php if (isset($value['defaultvalue']['data'])) : ?>
                                                <?php if (isset($value['prefixvalue'])) : ?>
                                                    <option value="<?= $value['prefixvalue']['value'] ?>"><?= $value['prefixvalue']['text'] ?></option>
                                                <?php endif; ?>

                                                <?php foreach ($value['defaultvalue']['data'] as $k => $v) : ?>
                                                    <?php if (!isset($value['defaultvalue']['type']) || $value['defaultvalue']['type'] == 'object') : ?>
                                                        <option value="<?= $v->{$value['defaultvalue']['value']} ?>" <?= (isset($value['defaultvalue']['selected']) && $value['defaultvalue']['selected'] == $v->{$value['defaultvalue']['value']} ? 'selected' : null) ?>><?= $v->{$value['defaultvalue']['text']} ?></option>
                                                    <?php elseif ($value['defaultvalue']['type'] == 'array') : ?>
                                                        <option value="<?= $v[$value['defaultvalue']['value']] ?>" <?= (isset($value['defaultvalue']['selected']) && $value['defaultvalue']['selected'] == $v[$value['defaultvalue']['value']] ? 'selected' : null) ?>><?= $v[$value['defaultvalue']['text']] ?></option>
                                                    <?php endif; ?>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </select>
                                    <?php elseif ($value['type'] == 'button') : ?>
                                        <div>
                                            <button type="submit" class="<?= $value['class'] ?>"><?= $value['text'] ?></button>
                                        </div>
                                    <?php endif; ?>
                                <?php else : ?>
                                    <?php if (isset($value['data'])) : ?>
                                        <div>
                                            <?php foreach ($value['data'] as $key => $value) : ?>
                                                <?php if ($value['type'] == 'button') : ?>
                                                    <button type="submit" class="<?= $value['class'] ?>"><?= $value['text'] ?></button>
                                                <?php elseif ($value['type'] == 'a') : ?>
                                                    <a href="<?= $value['href'] ?>" class="<?= $value['class'] ?>"><?= $value['text'] ?></a>
                                                <?php endif; ?>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </form>
        <?php endif; ?>

        <?php
        // Check if this is transaction page by checking URL
        $current_url = $_SERVER['REQUEST_URI'] ?? '';
        $is_transaction_page = strpos($current_url, 'transaction') !== false;
        ?>

        <?php if ($is_transaction_page): ?>
            <!-- Filter Section for Transactions -->
            <div class="row mb-3">
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="filter_date_from" class="form-label">Tanggal Dari</label>
                        <input type="date" id="filter_date_from" class="form-control">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="filter_date_to" class="form-label">Tanggal Sampai</label>
                        <input type="date" id="filter_date_to" class="form-control">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="filter_type" class="form-label">Filter Tipe Transaksi</label>
                        <select id="filter_type" class="form-select">
                            <option value="">Semua Tipe</option>
                            <option value="Pendapatan">Pendapatan</option>
                            <option value="Pengeluaran">Pengeluaran</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="filter_workunit" class="form-label">Filter Unit Usaha</label>
                        <select id="filter_workunit" class="form-select">
                            <option value="">Semua Unit Usaha</option>
                            <?php
                            // Get unique work units from the results
                            $unique_workunits = array();
                            if (isset($element['result'])) {
                                foreach ($element['result'] as $item) {
                                    $workunit_data = isset($item->workunitdata) ? $item->workunitdata : 'N/A';
                                    if (!isset($unique_workunits[$workunit_data]) && $workunit_data != 'N/A') {
                                        $unique_workunits[$workunit_data] = $workunit_data;
                                    }
                                }
                            }
                            foreach ($unique_workunits as $workunit): ?>
                                <option value="<?= $workunit ?>"><?= $workunit ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="filter_status" class="form-label">Filter Status</label>
                        <select id="filter_status" class="form-select">
                            <option value="">Semua Status</option>
                            <option value="Lunas">Lunas</option>
                            <option value="Menunggu Pembayaran">Menunggu Pembayaran</option>
                            <option value="Dibatalkan">Dibatalkan</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3 align-self-end">
                    <button type="button" id="reset_filters" class="btn btn-secondary btn-sm mt-4">
                        <i class="ri-refresh-line"></i> Reset Filter
                    </button>
                </div>
            </div>
        <?php endif; ?>

        <div class="table-responsive">
            <table class="table bordered-table datatables">
                <thead>
                    <tr>
                        <?php foreach ($element['table'] as $key => $value) : ?>
                            <?php if (!is_array($value)) : ?>
                                <th><?= $value ?></th>
                            <?php else : ?>
                                <?php foreach ($value as $k => $v) : ?>
                                    <?php if ($k == 'title') : ?>
                                        <th><?= $v ?></th>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </tr>
                </thead>

                <tbody>
                    <?php foreach ($element['result'] as $key => $value) : ?>
                        <tr>
                            <?php foreach ($element['table'] as $k => $v) : ?>
                                <?php if (!is_array($v)) : ?>
                                    <?php if (isset($value->$k) && (validateDate($value->$k, 'Y-m-d') || validateDate($value->$k, 'Y-m-d H:i:s'))) : ?>
                                        <?php if (validateDate($value->$k, 'Y-m-d')) : ?>
                                            <td><?= tgl_indo($value->$k) ?></td>
                                        <?php else : ?>
                                            <td><?= tgl_indo(date('Y-m-d', strtotime($value->$k))) ?> <?= date('H:i:s', strtotime($value->$k)) ?></td>
                                        <?php endif; ?>
                                    <?php else : ?>
                                        <?php if ($k == 'workunitdata') : ?>
                                            <td><?= isset($value->workunitdata) ? $value->workunitdata : 'N/A' ?></td>
                                        <?php elseif ($k == 'amount' && isset($value->$k)) : ?>
                                            <td>Rp <?= number_format($value->$k, 0, ',', '.') ?></td>
                                        <?php else : ?>
                                            <td><?= isset($value->$k) ? $value->$k : 'N/A' ?></td>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                <?php else : ?>
                                    <?php foreach ($v as $k2 => $v2) : ?>
                                        <?php if (is_array($v2)) : ?>
                                            <td>
                                                <?php foreach ($v2 as $k3 => $v3) : ?>
                                                    <?php $statement = ""; ?>

                                                    <?php if (isset($v3['statement'])) : ?>
                                                        <?php if (!isset($v3['statement']['type'])) : ?>
                                                            <?php $skipped = false; ?>

                                                            <?php foreach ($v3['statement'] as $k4 => $v4) : ?>
                                                                <?php if ($value->{$k4} != $v4) : ?>
                                                                    <?php $skipped = true; ?>
                                                                <?php endif; ?>
                                                            <?php endforeach; ?>

                                                            <?php if ($skipped) continue; ?>
                                                        <?php elseif ($v3['statement']['type'] == 'specific') : ?>
                                                            <?php foreach ($v3['statement']['columns'] as $k4 => $v4) : ?>
                                                                <?php if ($value->{$k4} != $v4) : ?>
                                                                    <?php foreach ($v3['statement']['actions'] as $k5 => $v5) : ?>
                                                                        <?php if ($k5 == 'hide') : ?>
                                                                            <?php $statement .= "style='display: none;'"; ?>
                                                                        <?php endif; ?>
                                                                    <?php endforeach; ?>
                                                                <?php endif; ?>
                                                            <?php endforeach; ?>
                                                        <?php endif; ?>
                                                    <?php endif; ?>

                                                    <?php
                                                    $attr = isset($v3['attr']) ? $v3['attr'] : array();

                                                    $attribute = "";
                                                    foreach ($attr as $k => $v) {
                                                        $attribute .= "$k='$v' ";
                                                    }
                                                    ?>

                                                    <?php if ($v3['type'] == 'img') : ?>
                                                        <?php $attribute = ""; ?>

                                                        <?php foreach ($v3['attr'] as $k4 => $v4) : ?>
                                                            <?php if (preg_match_all('/table.value/', $v4)) : ?>
                                                                <?php preg_match("/(?<={).*?(?=})/", $v4, $match); ?>
                                                                <?php $get_col = explode('.', $match[0]) ?>

                                                                <?php if (isset($get_col[2])) : ?>
                                                                    <?php $v4 = str_replace('${table.value.' . $get_col[2] . '}', $value->{$get_col[2]} ?? 'Data', $v4); ?>
                                                                <?php endif; ?>
                                                            <?php endif; ?>

                                                            <?php $attribute .= "$k4='$v4'"; ?>
                                                        <?php endforeach; ?>

                                                        <img <?= $attribute ?>>
                                                    <?php elseif ($v3['type'] == 'button') : ?>
                                                        <?php $onclick = str_replace('${table.primary}', $value->id, $v3['onclick']); ?>

                                                        <?php if (preg_match_all('/table.value/', $onclick)) : ?>
                                                            <?php preg_match("/(?<={).*?(?=})/", $onclick, $match); ?>
                                                            <?php $get_col = explode('.', $match[0]) ?>

                                                            <?php if (isset($get_col[2])) : ?>
                                                                <?php $onclick = str_replace('${table.value.' . $get_col[2] . '}', $value->{$get_col[2]} ?? 'Data', $onclick); ?>
                                                            <?php endif; ?>
                                                        <?php endif; ?>

                                                        <button type="button" class="<?= $v3['class'] ?>" onclick="<?= $onclick ?>" <?= isset($statement) ? $statement : null ?>>
                                                            <i class="<?= $v3['icon'] ?>"></i>

                                                            <?php if (isset($v3['text'])) : ?>
                                                                <span><?= $v3['text'] ?></span>
                                                            <?php endif; ?>
                                                        </button>
                                                    <?php elseif ($v3['type'] == 'a') : ?>
                                                        <?php $href = str_replace('${table.primary}', $value->id, $v3['href']); ?>

                                                        <?php if (preg_match_all('/table.value/', $href)) : ?>
                                                            <?php preg_match("/(?<={).*?(?=})/", $href, $match); ?>
                                                            <?php $get_col = explode('.', $match[0]) ?>

                                                            <?php if (isset($get_col[2])) : ?>
                                                                <?php $href = str_replace('${table.value.' . $get_col[2] . '}', $value->{$get_col[2]} ?? 'Data', $href); ?>
                                                            <?php endif; ?>
                                                        <?php endif; ?>

                                                        <a href="<?= $href ?>" class="<?= $v3['class'] ?>" <?= $attribute ?> <?= isset($statement) ? $statement : null ?>>
                                                            <i class="<?= $v3['icon'] ?>"></i>

                                                            <?php if (isset($v3['text'])) : ?>
                                                                <span><?= $v3['text'] ?></span>
                                                            <?php endif; ?>
                                                        </a>
                                                    <?php elseif ($v3['type'] == 'span') : ?>
                                                        <span><?= $v3['text'] ?></span>
                                                    <?php endif; ?>
                                                <?php endforeach; ?>
                                            </td>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php if ($is_transaction_page): ?>
    <script>
        window.onload = function() {
            var table = $('.datatables').DataTable();

            // Date range filter function
            var dateRangeFilter = null;

            function filterByDateRange() {
                var dateFrom = $('#filter_date_from').val();
                var dateTo = $('#filter_date_to').val();

                // Remove existing filter if any
                if (dateRangeFilter) {
                    var index = $.fn.dataTable.ext.search.indexOf(dateRangeFilter);
                    if (index > -1) {
                        $.fn.dataTable.ext.search.splice(index, 1);
                    }
                }

                if (dateFrom || dateTo) {
                    // Create new filter function for date range
                    dateRangeFilter = function(settings, data, dataIndex) {
                        var dateStr = data[1]; // Transaction date column

                        // Extract date from Indonesian format (dd/mm/yyyy)
                        var dateParts = dateStr.match(/(\d{2})\/(\d{2})\/(\d{4})/);
                        if (!dateParts) return true;

                        var rowDate = new Date(dateParts[3], dateParts[2] - 1, dateParts[1]);
                        var fromDate = dateFrom ? new Date(dateFrom) : null;
                        var toDate = dateTo ? new Date(dateTo) : null;

                        if (fromDate && toDate) {
                            return rowDate >= fromDate && rowDate <= toDate;
                        } else if (fromDate) {
                            return rowDate >= fromDate;
                        } else if (toDate) {
                            return rowDate <= toDate;
                        }

                        return true;
                    };

                    $.fn.dataTable.ext.search.push(dateRangeFilter);
                }

                table.draw();
            }

            // Date filter events
            $('#filter_date_from, #filter_date_to').on('change', filterByDateRange);

            // Transaction type filter
            $('#filter_type').on('change', function() {
                var type = this.value;
                if (type) {
                    // Filter by transaction type (column index 3)
                    table.column(3).search(type, false, false).draw();
                } else {
                    table.column(3).search('').draw();
                }
            });

            // Work unit filter
            $('#filter_workunit').on('change', function() {
                var workunit = this.value;
                if (workunit) {
                    // Filter by work unit (column index 4)
                    table.column(4).search(workunit, false, false).draw();
                } else {
                    table.column(4).search('').draw();
                }
            });

            // Status filter
            $('#filter_status').on('change', function() {
                var status = this.value;
                if (status) {
                    // Filter by status (column index 5)
                    table.column(5).search(status, false, false).draw();
                } else {
                    table.column(5).search('').draw();
                }
            });

            // Reset filters
            $('#reset_filters').on('click', function() {
                // Clear all filter inputs
                $('#filter_date_from').val('');
                $('#filter_date_to').val('');
                $('#filter_type').val('');
                $('#filter_workunit').val('');
                $('#filter_status').val('');

                // Remove custom date filter
                if (dateRangeFilter) {
                    var index = $.fn.dataTable.ext.search.indexOf(dateRangeFilter);
                    if (index > -1) {
                        $.fn.dataTable.ext.search.splice(index, 1);
                    }
                    dateRangeFilter = null;
                }

                // Clear all table column searches
                table.columns().search('').draw();
            });
        };
    </script>
<?php endif; ?>